/* Language Toggle Button Styling */
.btn-outline-light {
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #000;
  background-color: #fff;
  font-size: 0.85rem;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
    border-color: rgba(255, 255, 255, 0.5);
    color: #000;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    color: #000;
    background-color: #fff;
  }

  i {
    font-size: 0.9rem;
  }
}

/* RTL Support */
:host-context(html[dir="rtl"]) .btn-outline-light,
:host-context(html[lang="ar"]) .btn-outline-light {
  i {
    margin-left: 0.5rem;
    margin-right: 0;
  }
}