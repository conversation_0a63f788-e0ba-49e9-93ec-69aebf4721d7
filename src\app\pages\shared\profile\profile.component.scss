// Profile Component Styles

.profile-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  flex-direction: column;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 0.3rem solid #f3f3f3;
  border-top: 0.3rem solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 1rem;
  font-size: 1.1rem;
  color: #6c757d;
}

.profile-content {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .profile-content {
    padding: 0 10px;
  }

  .card {
    margin-bottom: 1rem;
  }

  .card-body {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .profile-content {
    padding: 0 5px;
  }

  .card-body {
    padding: 0.75rem;
  }

  .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }
}

// Ensure proper spacing
.card {
  margin-bottom: 1.5rem;
}

.card:last-child {
  margin-bottom: 0;
}

// RTL Support
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  direction: rtl;
  text-align: right;

  .card {
    direction: rtl;
    text-align: right;
  }

  .card-header {
    text-align: right;

    .card-title {
      justify-content: flex-end;

      h3 {
        text-align: right;
      }
    }
  }

  .card-body {
    text-align: right;
  }

  .form-label {
    text-align: right;
    justify-content: flex-end;
  }

  .form-control {
    text-align: right;
  }

  .btn {
    margin-left: 0;
    margin-right: 0.5rem;
  }

  // Fix Bootstrap grid alignment
  .row {
    direction: rtl;
  }

  .col-lg-4,
  .col-lg-8 {
    text-align: right;
  }

  // Fix required asterisk position
  .required::after {
    content: " *";
    color: #e74c3c;
    margin-left: 0;
    margin-right: 2px;
  }

  // Fix card footer alignment
  .card-footer {
    justify-content: flex-start !important;

    .btn {
      margin-left: 0;
      margin-right: 0.5rem;
    }
  }

  // Fix Bootstrap utilities for RTL
  .me-2, .me-3, .me-4, .me-5, .me-6 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }

  .ms-2, .ms-3, .ms-4, .ms-5, .ms-6 {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
  }

  .pe-2, .pe-3, .pe-4, .pe-5, .pe-6, .pe-7, .pe-8 {
    padding-right: 0 !important;
    padding-left: 1rem !important;
  }

  .ps-2, .ps-3, .ps-4, .ps-5, .ps-6, .ps-7, .ps-8 {
    padding-left: 0 !important;
    padding-right: 1rem !important;
  }

  // Fix text alignment
  .text-start {
    text-align: right !important;
  }

  .text-end {
    text-align: left !important;
  }

  // Fix flex utilities
  .justify-content-start {
    justify-content: flex-end !important;
  }

  .justify-content-end {
    justify-content: flex-start !important;
  }

  // Fix validation messages
  .invalid-feedback,
  .text-danger {
    text-align: right !important;
  }
}
