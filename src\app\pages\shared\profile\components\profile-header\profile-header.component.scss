// RTL Support
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .d-flex {
    &.flex-wrap.flex-sm-nowrap {
      flex-direction: row-reverse;

      .me-7 {
        margin-right: 0 !important;
        margin-left: 1.5rem !important;
        order: 2;
      }

      .flex-grow-1 {
        order: 1;
      }
    }

    &.justify-content-between {
      flex-direction: row !important;
      justify-content: space-between !important;
      align-items: flex-start !important;

      .d-flex.flex-column {
        text-align: right !important;
        flex: 1;
        order: 1;
      }

      .d-flex.my-4 {
        order: 2;
        margin-right: 0 !important;
        margin-left: 1rem !important;
        align-self: flex-start !important;
        flex-shrink: 0;
      }
    }
  }

  .me-3, .me-6, .me-2 {
    margin-right: 0 !important;
    margin-left: 0.75rem !important;
  }

  .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
  }

  .border-dashed {
    text-align: center;

    .d-flex.align-items-center.justify-content-center {
      justify-content: center !important;
    }

    .fw-bold.fs-6.text-gray-500.text-center {
      text-align: center !important;
    }
  }

  .fw-bold {
    text-align: center;
  }

  // إصلاح ترتيب الكاردات في العربية
  .d-flex.flex-wrap {
    &:not(.fw-bold) {
      justify-content: flex-start !important;

      .border.border-gray-300.border-dashed {
        margin-right: 0 !important;
        margin-left: 1.5rem !important;

        &:first-child {
          margin-left: 0 !important;
        }
      }
    }
  }

  // إصلاح أيقونة الكاميرا في العربية
  .btn-icon,
  .btn-circle {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    i {
      margin: 0 !important;
    }
  }

  // إصلاح موضع الكاميرا
  .position-absolute {
    &.bottom-0 {
      left: 50% !important;
      right: auto !important;
      transform: translateX(-50%) !important;
    }
  }

  // محاذاة النصوص في العربية
  .fs-2.fw-bolder {
    text-align: center !important;
  }

  .border-dashed {
    .d-flex.align-items-center {
      justify-content: center !important;
    }

    .fw-bold.fs-6 {
      text-align: center !important;
    }
  }

  // تحسين التخطيط العام
  .flex-grow-1 {
    .d-flex.justify-content-between {
      flex-direction: row !important;

      .d-flex.flex-column {
        text-align: right !important;
      }
    }
  }

  .d-flex.align-items-center.mb-2 {
    justify-content: flex-start !important;
    text-align: right !important;
    direction: rtl !important;
    width: 100% !important;

    .text-gray-800.fs-2.fw-bolder {
      text-align: right !important;
      margin-right: 0 !important;
      margin-left: 0 !important;
      order: 1;
    }

    .badge {
      margin-left: 0 !important;
      margin-right: 0.5rem !important;
      order: 2;
    }
  }

  // محاذاة معلومات الاتصال
  .d-flex.flex-wrap {
    justify-content: flex-start !important;
    text-align: right !important;
    direction: rtl !important;

    a {
      direction: rtl !important;
      text-align: right !important;
      margin-right: 0 !important;
      margin-left: 1.25rem !important;

      app-keenicon {
        margin-right: 0 !important;
        margin-left: 0.25rem !important;
      }
    }
  }

  .d-flex.my-4 {
    margin-right: auto !important;
    margin-left: 0 !important;
  }

  // إزالة المساحة الزائدة
  .pe-8 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .me-7 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
  }

  // إصلاح عام للمحاذاة في العربية
  .card-body {
    .d-flex.flex-wrap.flex-sm-nowrap.mb-3 {
      align-items: flex-start !important;
    }
  }

  // إصلاح موضع زر ترقية الخطة
  .d-flex.my-4.align-self-center {
    align-self: flex-start !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;

    .btn {
      white-space: nowrap;
    }
  }

  // إصلاح محاذاة الأيقونات مع النص
  app-keenicon {
    &.me-1 {
      margin-right: 0 !important;
      margin-left: 0.25rem !important;
    }
  }

  // إصلاح محاذاة البادج
  .badge {
    &.ms-2 {
      margin-left: 0 !important;
      margin-right: 0.5rem !important;
    }
  }
}
