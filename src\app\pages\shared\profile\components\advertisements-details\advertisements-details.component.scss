// Badge with delete button styles
.badge {
  padding: 0.5rem 1rem !important;
  position: relative;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;

  .btn {
    width: 15px;
    height: 15px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    opacity: 0;
    transition: all 0.2s ease;
    box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.1);

    i {
      font-size: 8px;
    }

    &:hover {
      background-color: #e6dadb !important;
      color: #ffffff !important;
    }
  }

  &:hover {
    .btn {
      opacity: 1;
    }
  }
}

// Modal styles
.modal-content {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
  border-radius: 0.475rem;

  .modal-header {
    background-color: #f1f8ff;
    border-bottom: 1px solid #eff2f5;

    .modal-title {
      color: #181c32;
    }
  }

  .modal-body {
    padding: 2rem;

    .form-control-solid,
    .form-select-solid {
      background-color: #f5f8fa;
      border-color: #f5f8fa;
      color: #5e6278;
      transition: color 0.2s ease, background-color 0.2s ease;

      &:focus {
        background-color: #eef3f7;
        border-color: #eef3f7;
      }
    }

    .form-text {
      font-size: 0.85rem;
    }
  }

  .modal-footer {
    border-top: 1px solid #eff2f5;
    padding: 1.5rem;
  }
}

// Specialization tree styles
.specialization-tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #eff2f5;
  border-radius: 0.475rem;
  padding: 1rem;
  background-color: #ffffff;
  box-shadow: 0 0.1rem 0.5rem rgba(0, 0, 0, 0.05);
}

.specialization-tree {
  .tree-node {
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
    border-radius: 0.475rem;

    &:hover {
      background-color: #f8f9fa;
    }

    .node-name {
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        color: #009ef7;
      }
    }
  }

  .tree-node-type {
    transition: all 0.2s ease;
    margin-bottom: 0.25rem;
    border-left: 2px solid #eff2f5;

    &:hover {
      background-color: #f8f9fa;
    }
  }

  .form-check-input {
    cursor: pointer;

    &:checked {
      background-color: #009ef7;
      border-color: #1ac228;
    }
  }

  .btn-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f1f8ff;
      color: #009ef7;
    }

    i {
      font-size: 0.8rem;
    }
  }
}

// RTL Support
:host-context(.rtl) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .form-label {
    text-align: right;
    justify-content: flex-end;
  }

  .badge {
    // تقليل حجم الـ badges في العربية
    padding: 0.375rem 0.75rem !important; // تقليل الـ py-4 px-5
    font-size: 0.8rem !important; // تقليل الـ fs-6
    margin-bottom: 0.25rem !important; // تقليل الـ mb-2
    margin-left: 0.5rem !important; // تقليل الـ me-5
    margin-right: 0 !important;
    display: inline-flex !important;

    .btn {
      margin-left: 0;
      margin-right: 5px;
      width: 12px !important;
      height: 12px !important;

      i {
        font-size: 6px !important;
      }
    }
  }

  .modal-content {
    direction: rtl;
    text-align: right;

    .modal-header {
      text-align: right;

      .modal-title {
        text-align: right;

        i {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }

      .btn-close {
        margin-left: auto;
        margin-right: 0;
      }
    }

    .modal-body {
      text-align: right;
      direction: rtl;

      .form-label {
        text-align: right;
        display: block;
      }

      .form-control, .form-select {
        text-align: right;
        direction: rtl;
      }

      .dropdown {
        .btn {
          text-align: right;

          .fas {
            margin-left: 0;
            margin-right: 0.5rem;
          }
        }
      }

      .dropdown-menu {
        text-align: right;
        direction: rtl;

        .dropdown-item {
          text-align: right;

          i {
            margin-left: 0.5rem;
            margin-right: 0;
          }
        }
      }

      .alert {
        text-align: right;

        i {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }

      .d-flex {
        flex-direction: row-reverse;

        &.align-items-center {
          flex-direction: row-reverse;
        }

        &.justify-content-between {
          flex-direction: row-reverse;
        }
      }
    }

    .modal-footer {
      text-align: left;
      justify-content: flex-start;

      .btn {
        margin-left: 0;
        margin-right: 0.5rem;

        &:last-child {
          margin-right: 0;
        }

        i {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }

  .specialization-tree {
    .tree-node {
      text-align: right;

      .d-flex {
        flex-direction: row-reverse;
      }
    }

    .tree-node-type {
      border-left: none;
      border-right: 2px solid #eff2f5;
      padding-right: 1rem;
      padding-left: 0;
    }

    .me-2, .me-5 {
      margin-right: 0 !important;
      margin-left: 0.5rem !important;
    }

    .ms-2 {
      margin-left: 0 !important;
      margin-right: 0.5rem !important;
    }

    .ms-3 {
      margin-left: 0 !important;
      margin-right: 0.75rem !important;
    }

    // تقليل المسافات بين الـ badges في العربية وجعلها جنب بعض
    .mb-2 {
      margin-bottom: 0.25rem !important;
    }

    .me-5 {
      margin-right: 0 !important;
      margin-left: 0.5rem !important; // مسافة أقل بين الـ badges
    }

    .py-4 {
      padding-top: 0.375rem !important; // تقليل الـ padding العمودي
      padding-bottom: 0.375rem !important;
    }

    .px-5 {
      padding-left: 0.75rem !important; // تقليل الـ padding الأفقي
      padding-right: 0.75rem !important;
    }

    .fs-6 {
      font-size: 0.8rem !important; // تقليل حجم الخط
    }

    .fw-bolder {
      font-weight: 600 !important; // تقليل سماكة الخط قليلاً
    }

    // جعل الـ badges تظهر في صف واحد
    .d-flex.align-items-center {
      display: inline-flex !important;
      flex-wrap: nowrap !important;
    }
  }

  .text-end {
    text-align: left !important;
  }

  .d-flex {
    flex-direction: row-reverse;

    &.justify-content-between {
      flex-direction: row-reverse;
    }
  }
}
